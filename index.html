<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>PortFolio - <PERSON><PERSON><PERSON></title>
  <meta name="description" content="">
  <meta name="keywords" content="">

  <!-- Favicons -->
  <link href="assets/img/Logo.jpg" rel="icon">
  <link href="assets/img/Logo.jpg" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Noto+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Questrial:wght@400&display=swap"
    rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <!-- =======================================================
  * Template Name: EasyFolio
  * Template URL: https://bootstrapmade.com/easyfolio-bootstrap-portfolio-template/
  * Updated: Feb 21 2025 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body class="index-page">

  <header id="header" class="header d-flex align-items-center sticky-top">
    <div
      class="header-container container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="index.html" class="logo d-flex align-items-center me-auto me-xl-0">
        <!-- Uncomment the line below if you also wish to use an image logo -->
        <!-- <img src="assets/img/logo.webp" alt=""> -->
        <h1 class="sitename">PortFolio</h1>
      </a>

      <nav id="navmenu" class="navmenu nav">
        <ul>
          <li><a href="#hero" class="active">Home</a></li>
          <li><a href="#about">About</a></li>
          <li><a href="#skills">Skills</a></li>
          <li><a href="#project">Projects</a></li>
          <li><a href="#education">Education</a></li>
          <li><a href="#certificates">Certification</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

      <div class="header-social-links">

        <a href="https://www.linkedin.com/in/mohasin-shaikh-2890a62b6/" class="linkedin"><i
            class="bi bi-linkedin"></i></a>
        <a href="https://github.com/mohasin9420" class="github"><i class="bi bi-github"></i></a>
      </div>

    </div>
  </header>

  <main class="main">

    <!-- Hero Section -->
    <section id="hero" class="hero section pad">
      <div class="container" data-aos="fade-up" data-aos-delay="100">
        <div class="row align-items-center content">

          <!-- Text Column -->
          <div class="col-lg-6 bold" data-aos="fade-up" data-aos-delay="200">
            <h3 class="headings role" style="color: black; font-weight: bold;">Hello, I am</h3>
            <h3 class="name" style="font-weight: bold; font-family: 'Rowdies', sans-serif;">Mohasin Mahammad Shaikh</h3>
            <h3 class="headings role" style="font-size: 2rem; font-weight: bold;">
              I am a
              <span class="headings typewrite" data-period="2000"
                data-type='[ "BTech Student.", "Full Stack Web Developer.", "Coder.", "Python Developer.", "Web Designer.",  "Continuous Learner." ]'
                style="font-family: 'Rowdies', sans-serif; font-size: 2rem;">
                <span class="wrap typewrite"></span>
              </span>
            </h3>
            <br>
            <div class="cta-buttons" data-aos="fade-up" data-aos-delay="300">
              <a href="#" class="btn btn-primary">
                Download CV <i class="bi bi-download ms-2 fs-5"></i>
              </a>
              <a href="#contact" class="btn btn-outline">
                Hire me <i class="bi bi-telephone-fill ms-2 fs-5"></i>
              </a>
            </div>

          </div>

          <!-- Image Column -->
          <div class="col-lg-6" data-aos="zoom-out" data-aos-delay="300">
            <div class="hero-image">
              <img src="/assets/img/profile/mohasin.jpg" alt="Portfolio Hero Image" class="img-fluid">
              <div class="shape-1"></div>
              <div class="shape-2"></div>
            </div>
          </div>

        </div>
      </div>
    </section>


    <!-- About Section -->
    <section id="about" class="about section light-background">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>About</h2>
        <div class="title-shape">
          <svg viewBox="0 0 200 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0,10 C 40,0 60,20 100,10 C 140,0 160,20 200,10" fill="none" stroke="currentColor"
              stroke-width="2"></path>
          </svg>
        </div>
        <div class="about-text">
          <p> Hi there! I'm Mohasin Mahammad Shaikh, a Web Developer skilled in HTML, CSS, JavaScript, React.js, Node.js,
            Express.js, MySQL and MongoDB.I am a Computer Science student with a passion for coding and creating apps
            and websites. I enjoy building real projects using Java,
            Python, HTML, CSS, and Android Studio. I’m a quick learner, team player, and always ready to take on new
            challenges to grow my skills.
            I create user-focused digital experiences that deliver results.
            I am currently pursuing a B. Tech. in Computer Science and Engineering at Punyashlok Ahilyadevi Holkar
            Solapur University,
            I blend theory
            with hands-on practice.
            I hold certifications from NPTEL, LinkedIn Learning, and Hackerrank
            showcasing my commitment to continuous learning.
            I have leading team quality because I am team leader in final year project, third year mini project and also in hackathons.
            I specialize in SEO-friendly, user-centric web solutions, aiming to exceed expectations and make a
            significant impact.
            Let's connect to create inspiring digital experiences that succeed. Together, we can turn ideas into
            reality!</p>
        </div>
      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row align-items-center justify-content-center">
          <!-- <div class="col-lg-6 position-relative" data-aos="fade-right" data-aos-delay="200">
            <div class="about-image">
              <img src="C:\Users\<USER>\OneDrive\Pictures\suraj photo.jpg" alt="Profile Image"
                class="img-fluid rounded-4">
            </div>
          </div> -->

          <div class="col-lg-6" data-aos="fade-left" data-aos-delay="300">
            <div class="about-content">
              <span class="subtitle">About Me</span>

              <h2>Full Stack Web Developer</h2>



              <div class="personal-info">
                <div class="row g-4">
                  <div class="col-6">
                    <div class="info-item">
                      <span class="label">Name</span>
                      <span class="value">Mohasin Mahammad Shaikh</span>
                    </div>
                  </div>

                  <div class="col-6">
                    <div class="info-item">
                      <span class="label">Phone</span>
                      <span class="value">+91 7028844513</span>
                    </div>
                  </div>

                  <div class="col-6">
                    <div class="info-item">
                      <span class="label">Age</span>
                      <span class="value">21 Years</span>
                    </div>
                  </div>


                  <div class="col-6">
                    <div class="info-item">
                      <span class="label">Occupation</span>
                      <span class="value">B.Tech Student</span>
                    </div>
                  </div>


                  <div class="col-10">
                    <div class="info-item">
                      <span class="label">Email</span>
                      <span class="value"><EMAIL></span>
                    </div>
                  </div>



                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

    </section><!-- /About Section -->

    <!-- Skills Section -->
    <section id="skills" class="skills section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Skills</h2>
        <div class="title-shape">
          <svg viewBox="0 0 200 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0,10 C 40,0 60,20 100,10 C 140,0 160,20 200,10" fill="none" stroke="currentColor"
              stroke-width="2"></path>
          </svg>
        </div>
      </div><!-- End Section Title -->
      <h2 style="font-weight: bold;">Technical Skills</h2>
      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <!-- Skill Buttons -->
        <div class="skills-buttons">
          <button class="skill-btn active" onclick="showSkillSection(event, 'frontend')">Front-End</button>
          <button class="skill-btn" onclick="showSkillSection(event, 'backend')">Back-End</button>
          <button class="skill-btn" onclick="showSkillSection(event, 'databases')">Databases</button>
          <button class="skill-btn" onclick="showSkillSection(event, 'languages')">Languages</button>
          <button class="skill-btn" onclick="showSkillSection(event, 'tools')">Tools & Technologies</button>
          <!-- <button class="skill-btn " onclick="showSkillSection(event, 'other')">Other Skills</button> -->
        </div>

        <!-- Front-End -->
        <div class="skill-section" id="frontend" style="display: flex;">
          <div class="card">
            <img src="assets/img/skills/HTML.png" alt="HTML" loading="lazy">
            <h4 class="headings"><b>HTML</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/CSS.svg" alt="CSS" loading="lazy">
            <h4 class="headings"><b>CSS</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/js.webp" alt="JavaScript" loading="lazy">
            <h4 class="headings"><b>JavaScript</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/bootstrap.png" alt="Bootstrap" loading="lazy">
            <h4 class="headings"><b>Bootstrap</b></h4>
          </div>
        </div>

        <!-- Back-End -->
        <div class="skill-section" id="backend" style="display: none;">
          <div class="card">
            <img src="assets/img/skills/node.png" alt="Node.js" loading="lazy">
            <h4 class="headings"><b>Node.js</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/flask.png" alt="flask" loading="lazy">
            <h4 class="headings"><b>Flask</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/springboot.png" alt="spring boot" loading="lazy">
            <h4 class="headings"><b>Spring Boot</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/php.png" alt="php" loading="lazy">
            <h4 class="headings"><b>php</b></h4>
          </div>
        </div>

        <!-- Databases -->
        <div class="skill-section" id="databases" style="display: none;">
          <div class="card">
            <img src="assets/img/skills/sql.jpg" alt="SQL" loading="lazy">
            <h4 class="headings"><b>MySQL</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/mongodb.webp" alt="MongoDB" loading="lazy">
            <h4 class="headings"><b>MongoDB</b></h4>
          </div>
        </div>

        <!-- Languages -->
        <div class="skill-section" id="languages" style="display: none;">
          <div class="card">
            <img src="assets/img/skills/c.png" alt="C" loading="lazy">
            <h4 class="headings"><b>C</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/java.jpeg" alt="java" loading="lazy">
            <h4 class="headings"><b>Java</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/python.jpg" alt="Python" loading="lazy">
            <h4 class="headings"><b>Python</b></h4>
          </div>
        </div>

        <!-- Tools -->
        <div class="skill-section" id="tools" style="display: none;">

          <div class="card">
            <img src="assets/img/skills/vscode.jpeg" alt="VS Code" loading="lazy">
            <h4 class="headings"><b>VS Code</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/eclipse.jpg" alt="eclipse" loading="lazy">
            <h4 class="headings"><b>Eclipse</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/git.png" alt="git" loading="lazy">
            <h4 class="headings"><b>Git</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/github.png" alt="github" loading="lazy">
            <h4 class="headings"><b>GitHub</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/flutter.png" alt="Flutter" loading="lazy">
            <h4 class="headings"><b>Flutter</b></h4>
          </div>
        </div>

        <!-- Other -->
        <div class="skill-section" id="other" style="display: none;">
          <div class="card">
            <img src="assets/img/skills/seo.png" alt="SEO" loading="lazy">
            <h4 class="headings"><b>SEO</b></h4>
          </div>
          <div class="card">
            <img src="assets/img/skills/prompt.png" alt="Prompt Engineering" loading="lazy">
            <h4 class="headings"><b>Prompt Engineering</b></h4>
          </div>
        </div>

      </div>
      <h2 class="section-title">Soft Skills</h2>
      <ul class="soft-skills">
        <li>🗣️ Communication</li>
        <li>🔍 Attention to Detail</li>
        <li>🧠 Critical Thinking</li>
        <li>🧭 Leadership</li>
        <li>📊 Logical Thinking</li>
        <li>🤝 Team Collaboration</li>
        <li>🔄 Adaptability</li>
        <li>⏱️ Time Management</li>
      </ul>


      </div>


    </section><!-- /Skills Section -->

    <!-- Project Section -->
    <section id="project" class="project section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Projects</h2>
        <div class="title-shape">
          <svg viewBox="0 0 200 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0,10 C 40,0 60,20 100,10 C 140,0 160,20 200,10" fill="none" stroke="currentColor"
              stroke-width="2"></path>
          </svg>
        </div>
      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="isotope-layout" data-default-filter="*" data-layout="masonry" data-sort="original-order">

          <!-- <div class="project-filters-container" data-aos="fade-up" data-aos-delay="200">
            <ul class="project-filters isotope-filters">
              <li data-filter="*" class="filter-active">All projects</li>
              <li data-filter=".filter-web">HTML Projects</li>
              <li data-filter=".filter-graphics">CSS Projects</li>
              <li data-filter=".filter-motion">JS Projects</li>
            </ul>
          </div> -->

          <div class="row g-4 isotope-container" data-aos="fade-up" data-aos-delay="300">

            <!-- Project 1: ResultZone -->
            <div class="col-lg-6 col-md-6 project-item" data-aos="fade-up" data-aos-delay="100">
              <div class="project-card">
                <div class="project-image">
                  <img src="assets/img/projects/mydigital.png" alt="ResultZone Project" class="img-fluid">
                  <div class="project-overlay">
                    <div class="project-links">
                      <a href="https://github.com/mohasin9420/Resultzone" target="_blank" class="project-link">
                        <i class="bi bi-github"></i>
                      </a>
                      <a href="#" class="project-details" data-bs-toggle="modal" data-bs-target="#projectModal1">
                        <i class="bi bi-eye"></i>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="project-content">
                  <h4>ResultZone – Student Result Display System</h4>
                  <p class="project-tech">Python • Flask • MySQL • HTML • CSS • JavaScript • Bootstrap</p>
                  <p class="project-description">Web-based student result management system with role-based authentication, PDF upload, and real-time result viewing.</p>
                </div>
              </div>
            </div>

            <!-- Project 2: SmartNetcafe -->
            <div class="col-lg-6 col-md-6 project-item" data-aos="fade-up" data-aos-delay="200">
              <div class="project-card">
                <div class="project-image">
                  <img src="assets/img/projects/attendance.jpg" alt="SmartNetcafe Project" class="img-fluid">
                  <div class="project-overlay">
                    <div class="project-links">
                      <a href="https://github.com/mohasin9420/SmartNetcafe" target="_blank" class="project-link">
                        <i class="bi bi-github"></i>
                      </a>
                      <a href="#" class="project-details" data-bs-toggle="modal" data-bs-target="#projectModal2">
                        <i class="bi bi-eye"></i>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="project-content">
                  <h4>SmartNetcafe – Cyber Cafe & e-Governance Platform</h4>
                  <p class="project-tech">Java • Spring Boot • Spring Security • MySQL • Thymeleaf • Bootstrap</p>
                  <p class="project-description">Dual-purpose application for cyber café management and government scheme applications with secure authentication.</p>
                </div>
              </div>
            </div>

            <!-- Project 3: Quickmeet -->
            <div class="col-lg-6 col-md-6 project-item" data-aos="fade-up" data-aos-delay="300">
              <div class="project-card">
                <div class="project-image">
                  <img src="assets/img/projects/calculator.jpg" alt="Quickmeet Project" class="img-fluid">
                  <div class="project-overlay">
                    <div class="project-links">
                      <a href="https://github.com/mohasin9420/quickmeet" target="_blank" class="project-link">
                        <i class="bi bi-github"></i>
                      </a>
                      <a href="https://quickmeet.free.nf/" target="_blank" class="project-link">
                        <i class="bi bi-globe"></i>
                      </a>
                      <a href="#" class="project-details" data-bs-toggle="modal" data-bs-target="#projectModal3">
                        <i class="bi bi-eye"></i>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="project-content">
                  <h4>Quickmeet – Real-Time Chat Room Web App</h4>
                  <p class="project-tech">PHP • MySQL • HTML • CSS • JavaScript</p>
                  <p class="project-description">Secure group chat application with password-protected rooms, file sharing, and admin dashboard.</p>
                </div>
              </div>
            </div>

            <!-- Project 4: Quick File Sharing -->
            <div class="col-lg-6 col-md-6 project-item" data-aos="fade-up" data-aos-delay="400">
              <div class="project-card">
                <div class="project-image">
                  <img src="assets/img/projects/qrcode.jpg" alt="Quick File Sharing Project" class="img-fluid">
                  <div class="project-overlay">
                    <div class="project-links">
                      <a href="https://github.com/mohasin9420/Quick" target="_blank" class="project-link">
                        <i class="bi bi-github"></i>
                      </a>
                      <a href="https://quickf.free.nf/" target="_blank" class="project-link">
                        <i class="bi bi-globe"></i>
                      </a>
                      <a href="#" class="project-details" data-bs-toggle="modal" data-bs-target="#projectModal4">
                        <i class="bi bi-eye"></i>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="project-content">
                  <h4>Quick – PHP File Sharing Portal</h4>
                  <p class="project-tech">PHP • MySQL • HTML • CSS</p>
                  <p class="project-description">Secure file sharing system with unique access codes, automatic expiry, and admin dashboard.</p>
                </div>
              </div>
            </div>

            <!-- Project 5: CodSeed -->
            <div class="col-lg-6 col-md-6 project-item" data-aos="fade-up" data-aos-delay="500">
              <div class="project-card">
                <div class="project-image">
                  <img src="assets/img/projects/spotifyclone.png" alt="CodSeed Project" class="img-fluid">
                  <div class="project-overlay">
                    <div class="project-links">
                      <a href="https://github.com/mohasin9420/Complier" target="_blank" class="project-link">
                        <i class="bi bi-github"></i>
                      </a>
                      <a href="https://codseed.free.nf/" target="_blank" class="project-link">
                        <i class="bi bi-globe"></i>
                      </a>
                      <a href="#" class="project-details" data-bs-toggle="modal" data-bs-target="#projectModal5">
                        <i class="bi bi-eye"></i>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="project-content">
                  <h4>CodSeed – Online Frontend Code Compiler</h4>
                  <p class="project-tech">HTML • CSS • JavaScript • Bootstrap</p>
                  <p class="project-description">Beginner-friendly platform to write and run frontend code with live preview and learning tools.</p>
                </div>
              </div>
            </div>

          </div><!-- End project Container -->

        </div>

      </div>

    </section><!-- /project Section -->

    <!-- Project Modals -->
    <!-- Modal 1: ResultZone -->
    <div class="modal fade" id="projectModal1" tabindex="-1" aria-labelledby="projectModal1Label" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="projectModal1Label">ResultZone – Web-Based Student Result Display System</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <img src="assets/img/projects/mydigital.png" alt="ResultZone Project" class="img-fluid mb-3">
            <p><strong>Tech Stack:</strong> Python, Flask, MySQL, HTML, CSS, JavaScript, Bootstrap</p>
            <ul>
              <li>Developed a responsive Flask web application for digitized student result management.</li>
              <li>Implemented role-based authentication (Admin/Student) and secure PDF upload for results.</li>
              <li>Enabled real-time result viewing, smart search (by PRN/Name/Dept), and performance analytics.</li>
              <li>Designed intuitive UI and integrated faculty profiles for enhanced academic insights.</li>
              <li>Reduced manual workload and improved accessibility and accuracy in result distribution.</li>
            </ul>
            <a href="https://github.com/mohasin9420/Resultzone" target="_blank" class="btn btn-primary">
              <i class="bi bi-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal 2: SmartNetcafe -->
    <div class="modal fade" id="projectModal2" tabindex="-1" aria-labelledby="projectModal2Label" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="projectModal2Label">SmartNetcafe – Spring Boot-Based Cyber Cafe & e-Governance Application Platform</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <img src="assets/img/projects/attendance.jpg" alt="SmartNetcafe Project" class="img-fluid mb-3">
            <p><strong>Tech Stack:</strong> Java, Spring Boot, Spring Security, MySQL, Thymeleaf, Bootstrap</p>
            <ul>
              <li>Designed and implemented a dual-purpose web application for cyber café management and online scheme application using Spring Boot.</li>
              <li>Enabled users to apply for government schemes, upload documents, and track application status via a user-friendly portal.</li>
              <li>Developed a secure role-based login system with admin features to monitor sessions, manage users, and verify scheme applications.</li>
              <li>Integrated session billing and auto-generated receipts, providing both users and operators with transparency and convenience.</li>
              <li>Optimized for real-world deployment in rural and semi-urban areas to support Digital India initiatives.</li>
            </ul>
            <a href="https://github.com/mohasin9420/SmartNetcafe" target="_blank" class="btn btn-primary">
              <i class="bi bi-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal 3: Quickmeet -->
    <div class="modal fade" id="projectModal3" tabindex="-1" aria-labelledby="projectModal3Label" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="projectModal3Label">Quickmeet – Real-Time Chat Room Web App</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <img src="assets/img/projects/calculator.jpg" alt="Quickmeet Project" class="img-fluid mb-3">
            <p><strong>Tech Stack:</strong> PHP, MySQL, HTML, CSS, JavaScript</p>
            <ul>
              <li>Built a secure, real-time group chat application with password-protected room access and support for text, images, and file sharing.</li>
              <li>Designed a custom admin dashboard for chat monitoring, analytics, and message/file management with SQL query support.</li>
              <li>Integrated WhatsApp sharing and emoji support for enhanced user engagement.</li>
              <li>Collaborated in a 4-member team, contributing to backend logic, system design, and user experience.</li>
            </ul>
            <div class="d-flex gap-2">
              <a href="https://github.com/mohasin9420/quickmeet" target="_blank" class="btn btn-primary">
                <i class="bi bi-github"></i> GitHub
              </a>
              <a href="https://quickmeet.free.nf/" target="_blank" class="btn btn-success">
                <i class="bi bi-globe"></i> Live Demo
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal 4: Quick File Sharing -->
    <div class="modal fade" id="projectModal4" tabindex="-1" aria-labelledby="projectModal4Label" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="projectModal4Label">Quick – PHP File Sharing Portal</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <img src="assets/img/projects/qrcode.jpg" alt="Quick File Sharing Project" class="img-fluid mb-3">
            <p><strong>Tech Stack:</strong> PHP, MySQL, HTML, CSS</p>
            <ul>
              <li>Developed a secure file sharing system allowing users to upload and download files using a unique 6-digit access code.</li>
              <li>Enabled optional metadata input (name, phone, date) and automatic file expiry after 7 days for privacy and storage efficiency.</li>
              <li>Built an admin dashboard for file management, search, deletion, and custom SQL reporting — no login required for users.</li>
              <li>Hosted live using free web hosting services; designed for quick, seamless sharing and internal collaboration use cases.</li>
            </ul>
            <div class="d-flex gap-2">
              <a href="https://github.com/mohasin9420/Quick" target="_blank" class="btn btn-primary">
                <i class="bi bi-github"></i> GitHub
              </a>
              <a href="https://quickf.free.nf/" target="_blank" class="btn btn-success">
                <i class="bi bi-globe"></i> Live Demo
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal 5: CodSeed -->
    <div class="modal fade" id="projectModal5" tabindex="-1" aria-labelledby="projectModal5Label" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="projectModal5Label">CodSeed – Online Frontend Code Compiler & Learning Portal</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <img src="assets/img/projects/spotifyclone.png" alt="CodSeed Project" class="img-fluid mb-3">
            <p><strong>Tech Stack:</strong> HTML, CSS, JavaScript, Bootstrap</p>
            <ul>
              <li>Developed a beginner-friendly online platform to write and run HTML, CSS, and JavaScript code in real time, without requiring any setup.</li>
              <li>Integrated a live preview area, syntax-highlighted editor, and auto-update features to assist learners with instant feedback.</li>
              <li>Included supportive learning tools like a whiteboard, template examples, and notepad systems for HTML, CSS, and JS.</li>
              <li>Designed an intuitive, responsive UI using Bootstrap to enhance usability for students and beginners.</li>
              <li>Hosted live at codseed.free.nf to provide open access for self-practice and demonstrations.</li>
            </ul>
            <div class="d-flex gap-2">
              <a href="https://github.com/mohasin9420/Complier" target="_blank" class="btn btn-primary">
                <i class="bi bi-github"></i> GitHub
              </a>
              <a href="https://codseed.free.nf/" target="_blank" class="btn btn-success">
                <i class="bi bi-globe"></i> Live Demo
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Resume Section -->
    <section id="education" class="education section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Education</h2>
        <div class="title-shape">
          <svg viewBox="0 0 200 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0,10 C 40,0 60,20 100,10 C 140,0 160,20 200,10" fill="none" stroke="currentColor"
              stroke-width="2"></path>
          </svg>
        </div>

      </div><!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row ">
          <div class="col-12">
            <div class="resume-wrapper">
              <div class="resume-block" data-aos="fade-up" data-aos-delay="100">
                <!-- <h2>My Education</h2> -->


                <div class="timeline align-content-center">
                  <div class="timeline-item" data-aos="fade-up" data-aos-delay="200">
                    <div class="timeline-left">
                      <h4 class="company">Maharashtra State Board Pune</h4>
                      <span class="period">2019-2020</span>
                    </div>
                    <div class="timeline-dot"></div>
                    <div class="timeline-right">
                      <h3 class="position">Secondary School Certificate (SSC) - 85%</h3>
                      <p class="description">From L.S.V.D.Vidhyalay, Jawala.</p>
                    </div>
                  </div>

                  <div class="timeline-item" data-aos="fade-up" data-aos-delay="300">
                    <div class="timeline-left">
                      <h4 class="company">Maharashtra State Board Pune</h4>
                      <span class="period">2021-2022</span>
                    </div>
                    <div class="timeline-dot"></div>
                    <div class="timeline-right">
                      <h3 class="position">Higher Secondary School Certificate (HSC) - 68.33%</h3>
                      <p class="description">From L.S.V.D.Vidhyalay, Jawala.</p>
                    </div>
                  </div>

                  <div class="timeline-item" data-aos="fade-up" data-aos-delay="400">
                    <div class="timeline-left">
                      <h4 class="company">Punyashlok Ahilyadevi Holkar Solapur University</h4>
                      <span class="period">2022-2026</span>
                    </div>
                    <div class="timeline-dot"></div>
                    <div class="timeline-right">
                      <h3 class="position">Bachelor of Technology (CSE) - CGPA: 8.13</h3>
                      <p class="description">From NBN Sinhgad College of Engineering, Solapur. Currently in 4th year.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

    </section><!-- /Resume Section -->



    <!-- Services Section -->

    <!-- Certificates Section - To be added later -->
    <section id="certificates" class="certificates section">
      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
        <h2>Certification</h2>
        <div class="title-shape">
          <svg viewBox="0 0 200 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0,10 C 40,0 60,20 100,10 C 140,0 160,20 200,10" fill="none" stroke="currentColor"
              stroke-width="2"></path>
          </svg>
        </div>
      </div><!-- End Section Title -->

      <div class="container py-5">
        <div class="row justify-content-center g-4">

          <!-- NPTEL Java Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/NPTEL java.png" alt="NPTEL Java Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>NPTEL Java Programming</h5>
                    <p>Advanced Java Programming Course</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Android Development Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/android.png" alt="Android Development Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Android Development</h5>
                    <p>Mobile App Development</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- CodSoft Internship Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/codsoft intership.png" alt="CodSoft Internship Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>CodSoft Internship</h5>
                    <p>Software Development Internship</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Science Internship Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/data science intership.png" alt="Data Science Internship Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Data Science Internship</h5>
                    <p>Data Analysis & Machine Learning</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Development and AWS Internship Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/dev and aws intership.png" alt="Development and AWS Internship Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Development & AWS Internship</h5>
                    <p>Cloud Development & AWS Services</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Generative AI Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/genative ai for all.png" alt="Generative AI Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Generative AI for All</h5>
                    <p>Artificial Intelligence & Machine Learning</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Java Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="700">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/java.png" alt="Java Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Java Programming</h5>
                    <p>Core Java Development</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- JavaScript Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="800">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/javascript.png" alt="JavaScript Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>JavaScript Programming</h5>
                    <p>Frontend & Backend JavaScript</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Power BI Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="900">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/powe bi.png" alt="Power BI Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Power BI</h5>
                    <p>Data Visualization & Analytics</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Python Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="1000">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/python.png" alt="Python Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Python Programming</h5>
                    <p>Backend Development & Data Science</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- React Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="1100">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/react.png" alt="React Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>React.js</h5>
                    <p>Frontend Framework Development</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Web Development Internship Certificate -->
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="1200">
            <div class="certificate-card">
              <div class="certificate-image">
                <img src="assets/img/certificates/web development intership.png" alt="Web Development Internship Certificate" class="img-fluid">
                <div class="certificate-overlay">
                  <div class="certificate-info">
                    <h5>Web Development Internship</h5>
                    <p>Full Stack Web Development</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </section><!-- /Certificates Section -->


    <!-- Contact Section -->
    <section id="contact" class="contact section light-background">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row g-5">
          <div class="col-lg-6">
            <div class="content" data-aos="fade-up" data-aos-delay="200">
              <div class="section-category mb-3">Contact</div>
              <!-- <h2 class="display-5 mb-4">Nemo enim ipsam voluptatem quia voluptas aspernatur</h2> -->


              <div class="contact-info mt-5">
                <div class="info-item d-flex mb-3">
                  <i class="bi bi-envelope-at me-3"></i>
                  <span><EMAIL></span>
                </div>

                <div class="info-item d-flex mb-3">
                  <i class="bi bi-telephone me-3"></i>
                  <span>+91 7028844513</span>
                </div>

                <div class="info-item d-flex mb-4">
                  <i class="bi bi-geo-alt me-3"></i>
                  <span>Jawala, Maharashtra, India</span>
                </div>

                <a href="https://maps.app.goo.gl/3NAwWMEt5Eg3GYti7" class="map-link d-inline-flex align-items-center">
                  Open Map
                  <i class="bi bi-arrow-right ms-2"></i>
                </a>
              </div>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="contact-form card" data-aos="fade-up" data-aos-delay="300">
              <div class="card-body p-4 p-lg-5">
                <form action="https://formsubmit.co/<EMAIL>" method="POST">
                  <!-- Hidden fields -->
                  <input type="hidden" name="_captcha" value="false">
                  <input type="hidden" name="_template" value="table">
                  <input type="hidden" name="_next" value="https://saurabhportfolio.free.nf/thankyou.html">

                  <div class="row gy-4">
                    <div class="col-12">
                      <input type="text" class="form-control" name="name" placeholder="Your Name" required>
                    </div>

                    <div class="col-12">
                      <input type="email" class="form-control" name="email" placeholder="Your Email" required>
                    </div>

                    <div class="col-12">
                      <textarea class="form-control" name="message" rows="6" placeholder="Message" required></textarea>
                    </div>

                    <button type="submit" class="btn btn-submit w-100">Submit Message</button>
                  </div>
                </form>



              </div>
            </div>
          </div>

        </div>

      </div>

    </section><!-- /Contact Section -->

  </main>

  <footer id="footer" class="footer">

    <div class="container">
      <div class="copyright text-center ">
        <p>© 2025 <span>Copyright</span> <strong class="px-1 sitename">PortFolio</strong>|| <span>All Rights
            Reserved</span>
        </p>
      </div>
      <div class="social-links d-flex justify-content-center">
        <a href="https://github.com/mohasin9420"><i class="bi bi-github"></i></a>
        <a href="https://www.linkedin.com/in/mohasin-shaikh-2890a62b6/"><i class="bi bi-linkedin"></i></a>
      </div>
      <div class="credits">
        <!-- All the links in the footer should remain intact. -->
        <!-- You can delete the links only if you've purchased the pro version. -->
        <!-- Licensing information: https://bootstrapmade.com/license/ -->
        <!-- Purchase the pro version with working PHP/AJAX contact form: [buy-url] -->
        Designed by <a href="https">SaurabhAglave</a>
      </div>
    </div>

  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i
      class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script src="assets/vendor/waypoints/noframework.waypoints.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
  <script src="assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>