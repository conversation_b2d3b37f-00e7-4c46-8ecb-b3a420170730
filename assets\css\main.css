/**
* Template Name: EasyFolio
* Template URL: https://bootstrapmade.com/easyfolio-bootstrap-portfolio-template/
* Updated: Feb 21 2025 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Questrial",  sans-serif;
  --nav-font: "Noto Sans",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root { 
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #0a0f14; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #0f2943; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #e87532; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #ffffff; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #0a0f14;  /* The default color of the main navmenu links */
  --nav-hover-color: #e87532; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #0a0f14; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #e87532; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #faf9fb;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #060606;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #252525;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}


h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 20px 0;
  transition: all 0.5s;
  z-index: 997;
}

.header .header-container {
  background: var(--surface-color);
  border-radius: 50px;
  padding: 5px 25px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 36px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 24px;
  margin: 0;
  padding-left: 5px;
  font-weight: 500;
  color: var(--heading-color);
}

.header .header-social-links {
  padding-right: 15px;
}

.header .header-social-links a {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  padding-left: 6px;
  display: inline-block;
  transition: 0.3s;
  font-size: 16px;
}

.header .header-social-links a:hover {
  color: var(--accent-color);
}

.header .header-social-links a i {
  line-height: 0px;
}

@media (max-width: 1200px) {
  .header {
    padding-top: 10px;
  }

  .header .header-container {
    margin-left: 10px;
    margin-right: 10px;
    padding: 10px 5px 10px 15px;
  }

  .header .logo {
    order: 1;
  }

  .header .header-social-links {
    order: 2;
  }

  .header .navmenu {
    order: 3;
  }
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 18px 15px;
    font-size: 16px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu li:last-child a {
    padding-right: 0;
  }

  .navmenu li:hover>a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover>a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover>ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown>.dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu>ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  padding: 40px 0;
  position: relative;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .social-links {
  margin-top: 20px;
}

.footer .social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  margin: 0 5px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.footer .credits {
  margin-top: 10px;
  font-size: 13px;
  text-align: center;
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  text-align: center;
  padding: 20px 0;
}

.page-title .breadcrumbs {
  margin-bottom: 1.5rem;
}

.page-title .breadcrumbs .breadcrumb {
  justify-content: center;
  margin: 0;
  padding: 0;
  background: none;
}

.page-title .breadcrumbs .breadcrumb .breadcrumb-item {
  font-size: 0.9rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.page-title .breadcrumbs .breadcrumb .breadcrumb-item.active {
  color: var(--accent-color);
}

.page-title .breadcrumbs .breadcrumb .breadcrumb-item a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.page-title .breadcrumbs .breadcrumb .breadcrumb-item a:hover {
  color: var(--accent-color);
}

.page-title .breadcrumbs .breadcrumb .breadcrumb-item a i {
  font-size: 0.9rem;
  margin-right: 0.2rem;
}

.page-title .breadcrumbs .breadcrumb .breadcrumb-item+.breadcrumb-item::before {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

.page-title .title-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem 0;
}

.page-title .title-wrapper h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  color: var(--heading-color);
}

.page-title .title-wrapper p {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .page-title .title-wrapper h1 {
    font-size: 2rem;
  }

  .page-title .title-wrapper p {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .page-title .title-wrapper h1 {
    font-size: 1.75rem;
  }
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 90px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 10px;
  background: linear-gradient(120deg, var(--heading-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.section-title .title-shape {
  width: 200px;
  height: 20px;
  margin: 0 auto;
  color: var(--accent-color);
  opacity: 0.5;
}

.section-title .title-shape svg {
  width: 100%;
  height: 100%;
}

.section-title p {
  margin: 15px auto 0;
  font-size: 16px;
  max-width: 950px;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  line-height: 1.8;
}

@media (max-width: 768px) {
  .section-title h2 {
    font-size: 32px;
  }

  .section-title .subtitle-wrapper .subtitle {
    font-size: 13px;
  }
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.pad{
  padding-top: 30px !important;
}

.hero .content h2 {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

@media (max-width: 991px) {
  .hero .content h2 {
    font-size: 2.5rem;
  }
}

.hero .content .lead {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.hero .cta-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

@media (max-width: 576px) {
  .hero .cta-buttons {
    flex-direction: column;
  }
}

.hero .cta-buttons .btn {
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.hero .cta-buttons .btn.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.hero .cta-buttons .btn.btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  border-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-2px);
}

.hero .cta-buttons .btn.btn-outline {
  border: 2px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  color: var(--accent-color);
  background: transparent;
}

.hero .cta-buttons .btn.btn-outline:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.hero .hero-stats {
  display: flex;
  gap: 3rem;
  margin-top: 2rem;
}

@media (max-width: 576px) {
  .hero .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
}



.hero .hero-stats .stat-item .stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 1;
}

.hero .hero-stats .stat-item .stat-label {
  font-size: 0.9rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-top: 0.5rem;
  display: block;
}
.hero .hero-image {
  position: relative;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero .hero-image img {
  height: 100%;
  max-height: 500px;
  width: 100%;
  max-width: 500px;
  border-radius: 50%;
  object-fit: cover;
  z-index: 2;
  border: 8px solid #00bfff;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

.hero .hero-image img:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: #e87532;
}

/* Morphing shapes */
.hero .hero-image .shape-1,
.hero .hero-image .shape-2 {
  position: absolute;
  border-radius: 30% 70% 70% 30%/30% 30% 70% 70%;
  z-index: 1;
}

.hero .hero-image .shape-1 {
  width: 60%;
  max-width: 300px;
  height: 60%;
  max-height: 300px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  top: -20px;
  right: -20px;
  animation: morphShape 15s linear infinite;
}

.hero .hero-image .shape-2 {
  width: 40%;
  max-width: 200px;
  height: 40%;
  max-height: 200px;
  background-color: color-mix(in srgb, var(--heading-color), transparent 90%);
  bottom: -20px;
  left: -20px;
  animation: morphShape 20s linear infinite reverse;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero .hero-image img {
    max-width: 350px;
    max-height: 350px;
  }

  .hero .hero-image .shape-1 {
    max-width: 200px;
    max-height: 200px;
  }

  .hero .hero-image .shape-2 {
    max-width: 160px;
    max-height: 160px;
  }
}

@media (max-width: 480px) {
  .hero .hero-image img {
    max-width: 220px;
    max-height: 220px;
  }

  .hero .hero-image .shape-1,
  .hero .hero-image .shape-2 {
    display: none; /* Optional: Hide morph shapes on very small screens */
  }
}

@keyframes morphShape {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
}


/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .about-image {
  position: relative;
  
}

.about .about-image img {
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

.about .about-content {
  padding-left: 2rem;
}

@media (max-width: 991.98px) {
  .about .about-content {
    padding-left: 0;
    margin-top: 3rem;
  }
}

.about .about-content .subtitle {
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 2px;
  color: var(--accent-color);
  margin-bottom: 1rem;
  display: block;
}

.about .about-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.about .about-content .lead {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
}

.about .personal-info {
  margin-top: 2rem;
  padding: 2rem;
  background-color: var(--surface-color);
  border-radius: 1rem;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
}

.about .personal-info .info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.about .personal-info .info-item .label {
  font-size: 0.875rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.about .personal-info .info-item .value {
  font-weight: 600;
  color: var(--heading-color);
}

.about .signature {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.about .signature .signature-image {
  max-width: 150px;
}

.about .signature .signature-info h4 {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.about .signature .signature-info p {
  font-size: 0.875rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0;
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills .skill-box {
  background-color: var(--surface-color);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.skills .skill-box h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.skills .skill-box p {
  font-size: 0.9rem;
  margin-bottom: 0.8rem;
  opacity: 0.6;
}

.skills .skill-box .progress {
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  border-radius: 50px;
  height: 8px;
  margin-top: 5px;
  overflow: hidden;
}

.skills .skill-box .progress .progress-bar {
  background-color: var(--accent-color);
  height: 100%;
  position: relative;
  border-radius: 50px;
  transition: 0.9s;
  width: 1px;
}

/*--------------------------------------------------------------
# Resume Section
--------------------------------------------------------------*/
.education .resume-wrapper .resume-block {
  margin-bottom: 3rem;
}

.education .resume-wrapper .resume-block:last-child {
  margin-bottom: 0;
}

.education .resume-wrapper .resume-block h2 {
  font-size: 2rem;
  margin-bottom: 0.8rem;
  font-weight: 700;
}

.education .resume-wrapper .resume-block h2 span {
  color: var(--accent-color);
}

.education .resume-wrapper .resume-block .lead {
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 3rem;
}

.education .resume-wrapper .timeline {
  position: relative;
}

.education .resume-wrapper .timeline::before {
  content: "";
  position: absolute;
  left: 20%;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
}

.education .resume-wrapper .timeline .timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2.5rem;
  position: relative;
}

.education .resume-wrapper .timeline .timeline-item:last-child {
  margin-bottom: 0;
}

.education .resume-wrapper .timeline .timeline-item .timeline-left {
  width: 20%;
  padding-right: 3rem;
}

.education .resume-wrapper .timeline .timeline-item .timeline-left .company {
  color: var(--heading-color);
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.education .resume-wrapper .timeline .timeline-item .timeline-left .period {
  color: var(--accent-color);
  font-size: 0.9rem;
}

.education .resume-wrapper .timeline .timeline-item .timeline-dot {
  position: absolute;
  left: calc(20% + 1px);
  transform: translateX(-50%);
  width: 1rem;
  height: 1rem;
  background-color: var(--accent-color);
  border-radius: 50%;
}

.education .resume-wrapper .timeline .timeline-item .timeline-right {
  width: 70%;
  padding-left: 3rem;
  margin-top: -4px;
}

.education .resume-wrapper .timeline .timeline-item .timeline-right .position {
  color: var(--heading-color);
  font-size: 1.2rem;
  margin-bottom: 0.6rem;
  font-weight: 600;
}

.education .resume-wrapper .timeline .timeline-item .timeline-right .description {
  color: color-mix(in srgb, var(--default-color), transparent 25%);
}

.education .resume-wrapper .timeline .timeline-item .timeline-right ul {
  padding-left: 15px;
}

.education .resume-wrapper .timeline .timeline-item .timeline-right ul li {
  padding-bottom: 12px;
}

@media (max-width: 992px) {
  .education .resume-wrapper .resume-block h2 {
    font-size: 2rem;
  }

  .education .resume-wrapper .timeline::before {
    left: 1rem;
  }

  .education .resume-wrapper .timeline .timeline-item {
    flex-direction: column;
    margin-bottom: 20px;
  }

  .education .resume-wrapper .timeline .timeline-item .timeline-left {
    width: 100%;
    text-align: left;
    padding-right: 0;
    padding-left: 2.5rem;
    margin-bottom: 1.6rem;
    margin-top: -4px;
  }

  .education .resume-wrapper .timeline .timeline-item .timeline-dot {
    left: calc(1rem + 1px);
  }

  .education .resume-wrapper .timeline .timeline-item .timeline-right {
    width: 100%;
    padding-left: 2.5rem;
  }
}


.col-12{
  display: flex;
  justify-content: center;
}

/*--------------------------------------------------------------
# Portfolio Section
--------------------------------------------------------------*/
.project .project-filters-container {
  margin-bottom: 40px;
}

.project .project-filters {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  padding: 0;
  margin: 0;
  list-style: none;
}

.project .project-filters li {
  font-size: 15px;
  font-weight: 500;
  padding: 8px 20px;
  cursor: pointer;
  border-radius: 30px;
  background-color: color-mix(in srgb, var(--surface-color), transparent 50%);
  color: var(--default-color);
  transition: all 0.3s ease-in-out;
}

.project .project-filters li:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.project .project-filters li.filter-active {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.project .project-card {
  background-color: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease-in-out;
}

.project .project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 35px rgba(0, 0, 0, 0.1);
}

.project .project-card:hover .project-overlay {
  opacity: 1;
  visibility: visible;
}

.project .project-card:hover .project-overlay .project-actions {
  transform: translateY(0);
}

.project .project-card .project-image {
  position: relative;
  overflow: hidden;
  aspect-ratio: 16/10;
}

.project .project-card .project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease-in-out;
}

.project .project-card .project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
  display: flex;
  align-items: flex-end;
  padding: 20px;
}

.project .project-card .project-overlay .project-actions {
  transform: translateY(20px);
  transition: all 0.4s ease-in-out;
  display: flex;
  gap: 15px;
}

.project .project-card .project-overlay .project-actions a {
  width: 45px;
  height: 45px;
  background-color: var(--surface-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  font-size: 20px;
  transition: all 0.3s ease;
}

.project .project-card .project-overlay .project-actions a:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: scale(1.1);
}

.project .project-card .project-content {
  padding: 25px;
}

.project .project-card .project-content .category {
  font-size: 14px;
  color: var(--accent-color);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
}

.project .project-card .project-content h3 {
  font-size: 20px;
  margin: 0 0 15px;
  font-weight: 600;
  transition: color 0.3s ease;
}



.project .project-card .project-content a {
  display: inline-block;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 600;
  color: #fff;
  background-color: #00bfff; /* Dark gray */
  text-decoration: none;
  border-radius: 25px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.project .project-card .project-content a:hover {
  background-color: #70a1ff; /* Slightly darker */
  transform: scale(1.05);
}


/* .project .project-card .project-content h3:hover {
  color: var(--accent-color);
} */

.project .project-card .project-content p {
  font-size: 15px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 0;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .project .project-filters li {
    font-size: 14px;
    padding: 6px 15px;
  }

  .project .project-card .project-content {
    padding: 20px;
  }

  .project .project-card .project-content h3 {
    font-size: 18px;
  }

  .project .project-card .project-content p {
    font-size: 14px;
  }
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials .testimonials-slider {
  width: 100%;
  position: relative;
  padding-bottom: 60px;
}

.testimonials .swiper-wrapper {
  height: auto;
}

.testimonials .testimonial-item {
  background-color: var(--surface-color);
  padding: 40px;
  border-radius: 20px;
}

@media (max-width: 575px) {
  .testimonials .testimonial-item {
    padding: 20px;
  }
}

.testimonials .testimonial-item h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

.testimonials .testimonial-item p {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 30px;
  font-style: italic;
}

.testimonials .testimonial-item .profile {
  gap: 15px;
}

.testimonials .testimonial-item .profile .profile-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonials .testimonial-item .profile .profile-info h3 {
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}

.testimonials .testimonial-item .profile .profile-info span {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color) 70%, transparent);
}

.testimonials .testimonial-item .featured-img-wrapper {
  min-height: 400px;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
}

.testimonials .testimonial-item .featured-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.testimonials .swiper-navigation {
  position: absolute;
  bottom: 0;
  gap: 10px;
}

.testimonials .swiper-button-prev,
.testimonials .swiper-button-next {
  position: relative;
  left: auto;
  right: auto;
  top: auto;
  margin: 0;
  width: 44px;
  height: 44px;
  background-color: var(--surface-color);
  border-radius: 50%;
  border: 1px solid color-mix(in srgb, var(--default-color) 10%, transparent);
  transition: 0.3s;
}

.testimonials .swiper-button-prev::after,
.testimonials .swiper-button-next::after {
  font-size: 20px;
  color: var(--default-color);
}

.testimonials .swiper-button-prev:hover,
.testimonials .swiper-button-next:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.testimonials .swiper-button-prev:hover::after,
.testimonials .swiper-button-next:hover::after {
  color: var(--contrast-color);
}

/*--------------------------------------------------------------
# certificates Section
--------------------------------------------------------------*/
.certificates {
  padding: 80px 0;
}

.certificates .servies-title {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.certificates .btn-outline-primary {
  color: var(--accent-color);
  border-color: var(--accent-color);
  padding: 10px 30px;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.certificates .btn-outline-primary:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.certificates .certificate-item{
  background-color: var(--surface-color);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  height: 100%;
  transition: transform 0.3s ease;
}

.certificates .certificate-item:hover {
  transform: scale(1.05);
}

.certificates .certificate-item i {
  font-size: 2.5rem;
  color: var(--accent-color);
  display: inline-block;
  margin-bottom: 15px;
}

.certificates .certificate-item h3 {
  font-size: 1.25rem;
  margin-bottom: 15px;
}

.certificates .certificate-item h3 a {
  color: var(--heading-color);
}

.certificates .certificate-item h3 a:hover {
  color: var(--accent-color);
}

.certificates .certificate-item p {
  font-size: 0.9rem;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
}

@media (max-width: 991px) {
  .certificates h2 {
    font-size: 2rem;
  }
}

/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .section-category {
  font-family: var(--heading-font);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact .content h2 {
  font-weight: 700;
  line-height: 1.2;
}

.contact .content .lead {
  color: color-mix(in srgb, var(--default-color), transparent 25%);
}

.contact .contact-info .info-item {
  font-size: 1.1rem;
}

.contact .contact-info .info-item i {
  color: var(--accent-color);
  font-size: 1.4rem;
}

.contact .contact-info .map-link {
  color: var(--heading-color);
  font-weight: 500;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.contact .contact-info .map-link i {
  transition: transform 0.3s ease;
}

.contact .contact-info .map-link:hover {
  color: var(--accent-color);
}

.contact .contact-info .map-link:hover i {
  transform: translateX(5px);
}

.contact .contact-form {
  background-color: var(--surface-color);
  border: none;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.contact .contact-form .form-control,
.contact .contact-form .form-select {
  padding: 0.75rem 1.25rem;
  border-color: #e5e5e5;
  border-radius: 8px;
  font-size: 1rem;
}

.contact .contact-form .form-control:focus,
.contact .contact-form .form-select:focus {
  border-color: color-mix(in srgb, var(--accent-color), transparent 50%);
  box-shadow: 0 0 0 0.25rem color-mix(in srgb, var(--accent-color), transparent 90%);
}

.contact .contact-form .form-control::placeholder,
.contact .contact-form .form-select::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

.contact .contact-form .btn-submit {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  padding: 0.75rem 2rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.contact .contact-form .btn-submit:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-1px);
}

.contact .contact-form .btn-submit:active {
  transform: translateY(0);
}

/*--------------------------------------------------------------
# Portfolio Details Section
--------------------------------------------------------------*/
.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.portfolio-details .portfolio-info h3:after {
  content: "";
  position: absolute;
  display: block;
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  bottom: 0;
}

.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.portfolio-details .portfolio-info ul li {
  display: flex;
  flex-direction: column;
  padding-bottom: 15px;
}

.portfolio-details .portfolio-info ul strong {
  text-transform: uppercase;
  font-weight: 400;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 14px;
}

.portfolio-details .portfolio-info .btn-visit {
  padding: 8px 40px;
  background: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 50px;
  transition: 0.3s;
}

.portfolio-details .portfolio-info .btn-visit:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 20%);
}

.portfolio-details .portfolio-description h2 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
  padding: 0;
}

.portfolio-details .portfolio-description .testimonial-item {
  padding: 30px 30px 0 30px;
  position: relative;
  background: color-mix(in srgb, var(--default-color), transparent 97%);
  margin-bottom: 50px;
}

.portfolio-details .portfolio-description .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50px;
  border: 6px solid var(--background-color);
  float: left;
  margin: 0 10px 0 0;
}

.portfolio-details .portfolio-description .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0 5px 0;
  padding-top: 20px;
}

.portfolio-details .portfolio-description .testimonial-item h4 {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.portfolio-details .portfolio-description .testimonial-item .quote-icon-left,
.portfolio-details .portfolio-description .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, var(--accent-color), transparent 50%);
  font-size: 26px;
  line-height: 0;
}

.portfolio-details .portfolio-description .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.portfolio-details .portfolio-description .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.portfolio-details .portfolio-description .testimonial-item p {
  font-style: italic;
  margin: 0 0 15px 0 0 0;
  padding: 0;
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/
.service-details .service-details-slider {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  z-index: 1;
}

.service-details .service-details-slider .swiper-wrapper {
  height: auto !important;
}

.service-details .service-details-slider .swiper-pagination {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  z-index: 2;
}

.service-details .service-details-slider .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: var(--contrast-color);
  opacity: 0.5;
  transition: 0.3s;
}

.service-details .service-details-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
  width: 20px;
  border-radius: 5px;
}

.service-details .content h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 20px;
}

.service-details .content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 15px;
}

.service-details .content .features .feature-box {
  padding: 20px;
  background: var(--surface-color);
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  height: 100%;
  transition: 0.3s;
}

.service-details .content .features .feature-box:hover {
  transform: translateY(-5px);
}

.service-details .content .features .feature-box i {
  font-size: 24px;
  color: var(--accent-color);
  margin-right: 15px;
}

.service-details .content .features .feature-box h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.service-details .service-info {
  background: var(--surface-color);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.service-details .service-info h4 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid color-mix(in srgb, var(--accent-color), transparent 85%);
}

.service-details .service-info .info-item {
  margin-bottom: 25px;
}

.service-details .service-info .info-item:last-child {
  margin-bottom: 0;
}

.service-details .service-info .info-item i {
  font-size: 24px;
  color: var(--accent-color);
  margin-bottom: 10px;
}

.service-details .service-info .info-item h5 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.service-details .service-info .info-item p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
}

.service-details .related-services {
  background: var(--surface-color);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.service-details .related-services h4 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid color-mix(in srgb, var(--accent-color), transparent 85%);
}

.service-details .related-services .service-item {
  margin-bottom: 25px;
  padding-bottom: 25px;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.service-details .related-services .service-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.service-details .related-services .service-item i {
  font-size: 24px;
  color: var(--accent-color);
  margin-bottom: 10px;
}

.service-details .related-services .service-item h5 {
  font-size: 18px;
  margin-bottom: 10px;
}

.service-details .related-services .service-item h5 a {
  color: var(--heading-color);
  transition: 0.3s;
}

.service-details .related-services .service-item h5 a:hover {
  color: var(--accent-color);
}

.service-details .related-services .service-item p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
  font-size: 14px;
}

@media (max-width: 991px) {

  .service-details .service-info,
  .service-details .related-services {
    margin-top: 30px;
  }
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}

.headings {
  font-family: 'Rowdies', sans-serif;
  color: #222;
  margin: 10px 0;
}

.intro-text {
  font-size: 1.5rem;
  color: #555;
}

.name {
  font-size: 2.5rem;
  color: #0d6efd;
  
}

.ampersand {
  font-size: 1.5rem;
  color: #999;
}

.role {
  font-size: 1.75rem;
  color: #333;
}

.typewrite > .wrap {
  border-right: 0.08em solid #666;
}







/* jsh */

.source-code, .view-project{
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  width: 100% !important;
  
  margin-left: 5px;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.view-project {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.view-project:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  border-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-2px);
}


.source-code {
  border: 2px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  color: var(--accent-color);
  background: transparent;
}

.source-code:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.project-btn {
  display: flex;
  justify-content: space-between;
}

/* Container */
.container {
  max-width: 1200px;
  margin: auto;
  padding: 40px 20px;
  font-family: "Segoe UI", sans-serif;
}

/* Buttons row */
.skills-buttons {
  text-align: center;
  margin-bottom: 30px;
}

/* Individual button */
.skill-btn {
  padding: 10px 18px;
  margin: 8px;
  font-size: 1rem;
  border: none;
  border-radius: 8px;
  background-color: #f0f0f0;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s, color 0.3s;
}

.skill-btn:hover {
  background-color: #e0e0e0;
}

.skill-btn.active {
  background-color: #007bff;
  color: #fff;
}

/* Skill section layout */
.skill-section {
  display: none;
  animation: fadeEffect 0.3s ease-in-out;
}

/* Cards container (use flexbox grid) */
.skill-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 25px;
  margin-top: 30px;
}

.skill-section {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 25px;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 20px;
}

/* Optional scroll style */
.skill-section::-webkit-scrollbar {
  height: 8px;
}
.skill-section::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}


/* Individual card */
.skills .card {
  background: #ffffff;
  padding: 20px;
  width: 160px;
  text-align: center;
  align-items: center;
  border: none !important;
  transition: transform 0.3s, box-shadow 0.3s;
}

.skills .card:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Card image */
.skills .card img {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-bottom: 12px;
  border-radius: 12px;
}

.skills h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 20px;
}

/* Headings inside card */
.skills .card h4 {
  font-size: 1rem;
  margin: 0;
}

.about-text p {
  line-height: 1.8;           /* Increases space between lines */
  font-size: 1.1rem;          /* Slightly larger text for better readability */
  margin-bottom: 1rem;        /* Adds space below the paragraph */
  text-align: justify;        /* Aligns text evenly on both sides */
  color: #333;                /* Optional: makes text a soft black */
}

.about-text {
  padding: 1.5rem;
  background-color: #fdfdfd; /* Optional: Light background */
  border-radius: 8px;        /* Rounded corners */
}


.skills .section-title {
  font-weight: bold;
  font-size: 2rem;
  color: #222;
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
}

.skills .soft-skills {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
  max-width: 800px;
  margin: 0 auto;
}

.skills .soft-skills li {
  background-color: #fcebea;
  color: #b71c1c;
  padding: 12px 16px;
  border-radius: 10px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.skills .soft-skills li:hover {
  transform: translateY(-3px);
  background-color: #f8d7da;
}


.certificates .card {
  background-color: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease-in-out;
}

.certificates .card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 35px rgba(0, 0, 0, 0.1);
}